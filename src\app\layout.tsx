import ReactQueryProvider from '@/components/provider/react-query'
import { Clerk<PERSON>rovider } from '@clerk/nextjs'
import type { Metadata } from 'next'
import { <PERSON>ei<PERSON>, <PERSON>eist_Mono } from 'next/font/google'
import './globals.css'
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'
import { Toaster } from '@/components/ui/sonner'
const geistSans = Geist({
  variable: '--font-geist-sans',
  subsets: ['latin'],
})

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
})

export const metadata: Metadata = {
  title: 'Clerk Next.js Quickstart',
  description: 'Generated by create next app',
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <ClerkProvider>
      <html lang="en">
        <body className={`${geistSans.variable} ${geistMono.variable} overflow-x-hidden`}>
          <ReactQueryProvider>
            {children}
            <ReactQueryDevtools initialIsOpen />
          </ReactQueryProvider>
          <Toaster
            position="top-center"
            toastOptions={{
              classNames: {
                success: '!bg-green-500',
                error: '!bg-red-500',
                title: '!text-white',
                icon: '!text-white',
              },
            }}
          />
        </body>
      </html>
    </ClerkProvider>
  )
}
