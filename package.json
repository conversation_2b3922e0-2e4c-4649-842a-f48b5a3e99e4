{"name": "coursity", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "npx @biomejs/biome check --write ./src"}, "dependencies": {"@clerk/nextjs": "^6.15.1", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.10", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tooltip": "^1.2.7", "@t3-oss/env-nextjs": "^0.12.0", "@tabler/icons-react": "^3.33.0", "@tanstack/react-query": "^5.75.4", "@tanstack/react-query-devtools": "^5.75.4", "@tanstack/react-router": "^1.127.8", "@tanstack/react-table": "^8.21.3", "@types/lodash": "^4.17.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "cobe": "^0.6.3", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "hls.js": "^1.6.2", "lodash": "^4.17.21", "lucide-react": "^0.488.0", "motion": "^12.9.2", "next": "15.3.0", "next-themes": "^0.4.6", "plyr": "^3.7.8", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "react-player": "^2.16.0", "react-use-measure": "^2.1.7", "sonner": "^2.0.5", "svix": "^1.64.1", "tailwind-merge": "^3.2.0", "tailwindcss-animated": "^2.0.0", "tw-animate-css": "^1.2.5", "zod": "^3.25.51"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4.1.4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.0", "postcss": "^8.5.3", "tailwindcss": "^4.1.4", "typescript": "^5"}}